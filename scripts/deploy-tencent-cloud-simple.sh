#!/bin/bash

# OCTI智能评估系统 - 腾讯云生产环境部署脚本
# 版本: v4.0.0 (基于成功的deploy-production.sh重构)
# 
# 功能特性:
# - 简化的部署流程，基于已验证的本机部署脚本
# - 智能数据库迁移处理
# - 腾讯云镜像源优化
# - 支持简化版和完整版部署
# - 完整的健康检查

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 全局变量
DEPLOYMENT_MODE="simple"
FORCE_REBUILD=false
DOMAIN=""
EMAIL=""

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --mode)
                DEPLOYMENT_MODE="$2"
                shift 2
                ;;
            --force-rebuild)
                FORCE_REBUILD=true
                shift
                ;;
            --domain)
                DOMAIN="$2"
                shift 2
                ;;
            --email)
                EMAIL="$2"
                shift 2
                ;;
            clean)
                FORCE_REBUILD=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 显示帮助信息
show_help() {
    echo "OCTI腾讯云部署脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --mode <simple|full>   部署模式 (默认: simple)"
    echo "  --force-rebuild        强制重新构建"
    echo "  --domain <域名>        配置域名 (仅full模式)"
    echo "  --email <邮箱>         SSL证书邮箱 (仅full模式)"
    echo "  clean                  完全清理后重新部署"
    echo "  -h, --help             显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 简化版部署"
    echo "  $0 --mode full --domain example.com  # 完整版部署"
    echo "  $0 clean --mode simple               # 清理后部署简化版"
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."

    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi

    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi

    # 检查版本
    local docker_version=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    local compose_version=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    
    log_info "Docker版本: $docker_version"
    log_info "Docker Compose版本: $compose_version"

    log_success "依赖检查完成"
}

# 环境配置检查
check_environment() {
    log_info "检查环境配置..."

    # 检查环境变量文件
    if [ ! -f ".env.production" ]; then
        log_warning ".env.production文件不存在，将从模板创建"
        if [ -f ".env.example" ]; then
            cp .env.example .env.production
            log_info "已创建.env.production文件，请根据需要修改配置"
        else
            log_error ".env.example模板文件不存在"
            exit 1
        fi
    fi

    # 检查必要的配置文件
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml文件不存在"
        exit 1
    fi

    log_success "环境配置检查完成"
}

# 清理现有部署
cleanup_existing() {
    if [[ "$FORCE_REBUILD" == "true" ]]; then
        log_info "清理现有部署..."
        
        # 停止并删除容器
        docker-compose --env-file .env.production down --remove-orphans || true
        
        # 清理镜像（可选）
        if docker images | grep -q "octi-production"; then
            log_info "删除现有镜像..."
            docker rmi octi-production:latest || true
        fi
        
        # 清理构建缓存
        log_info "清理Docker构建缓存..."
        docker builder prune -f || true
        
        log_success "清理完成"
    fi
}

# 构建镜像
build_images() {
    log_info "构建生产环境Docker镜像..."

    local build_args=""
    if [[ "$FORCE_REBUILD" == "true" ]]; then
        build_args="--no-cache"
        log_info "强制重新构建（清除缓存）..."
    else
        log_info "使用缓存构建..."
    fi

    # 构建镜像
    docker-compose --env-file .env.production build $build_args

    log_success "镜像构建完成"
}

# 部署服务
deploy_services() {
    log_info "部署生产环境服务..."

    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose --env-file .env.production down || true

    # 启动新服务
    log_info "启动新服务..."
    docker-compose --env-file .env.production up -d

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 15

    log_success "服务部署完成"
}

# 数据库初始化和迁移
init_database() {
    log_info "初始化数据库..."

    # 等待数据库就绪
    log_info "等待数据库就绪..."
    local timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose --env-file .env.production exec -T postgres pg_isready -U postgres -d octi &> /dev/null; then
            break
        fi
        log_info "等待数据库连接... (剩余${timeout}秒)"
        sleep 2
        timeout=$((timeout-2))
    done

    if [ $timeout -le 0 ]; then
        log_error "数据库启动超时"
        return 1
    fi

    log_success "数据库连接成功"

    # 智能数据库迁移处理
    log_info "执行数据库迁移..."
    
    # 首先尝试标准迁移
    if docker-compose --env-file .env.production exec -T app npx prisma migrate deploy 2>/dev/null; then
        log_success "数据库迁移成功"
    else
        log_warning "标准迁移失败，尝试智能修复..."
        
        # 检查是否是P3005错误（数据库不为空）
        local migration_output=$(docker-compose --env-file .env.production exec -T app npx prisma migrate deploy 2>&1 || true)
        
        if echo "$migration_output" | grep -q "P3005\|database is not empty"; then
            log_info "检测到P3005错误，标记现有数据库为基线..."
            
            # 获取最新的迁移文件并标记为已应用
            local latest_migration=$(docker-compose --env-file .env.production exec -T app sh -c "find prisma/migrations -name '*.sql' 2>/dev/null | tail -1 | xargs basename -s .sql 2>/dev/null || echo ''" | tr -d '\r')
            
            if [[ -n "$latest_migration" ]]; then
                log_info "标记迁移为已应用: $latest_migration"
                if docker-compose --env-file .env.production exec -T app npx prisma migrate resolve --applied "$latest_migration" 2>/dev/null; then
                    log_success "基线标记成功"
                    
                    # 再次尝试迁移
                    if docker-compose --env-file .env.production exec -T app npx prisma migrate deploy 2>/dev/null; then
                        log_success "基线标记后迁移成功"
                    fi
                fi
            else
                log_info "未找到迁移文件，可能是首次部署"
                # 尝试强制同步
                if docker-compose --env-file .env.production exec -T app npx prisma db push --accept-data-loss 2>/dev/null; then
                    log_success "数据库强制同步成功"
                fi
            fi
        else
            log_warning "其他迁移错误，尝试生成客户端..."
        fi
    fi

    # 生成Prisma客户端
    log_info "生成Prisma客户端..."
    if docker-compose --env-file .env.production exec -T app npx prisma generate 2>/dev/null; then
        log_success "Prisma客户端生成成功"
    else
        log_warning "Prisma客户端生成失败，但不影响应用运行"
    fi

    log_success "数据库初始化完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."

    # 检查容器状态
    log_info "检查容器状态..."
    docker-compose --env-file .env.production ps

    # 等待应用就绪
    log_info "等待应用就绪..."
    local timeout=120
    local url="http://localhost:3000/api/health"
    
    while [ $timeout -gt 0 ]; do
        if curl -f "$url" &> /dev/null; then
            break
        fi
        log_info "等待应用响应... (剩余${timeout}秒)"
        sleep 5
        timeout=$((timeout-5))
    done

    if [ $timeout -le 0 ]; then
        log_error "应用健康检查失败"
        log_info "查看应用日志:"
        docker-compose --env-file .env.production logs app --tail 20
        return 1
    fi

    # 显示健康检查结果
    log_info "应用健康检查结果:"
    curl -s "$url" | jq '.' 2>/dev/null || curl -s "$url"

    log_success "健康检查通过"
}

# 显示部署信息
show_deployment_info() {
    log_success "=== 部署完成 ==="
    echo
    log_info "服务访问地址:"
    echo "  应用地址: http://localhost:3000"
    echo "  健康检查: http://localhost:3000/api/health"
    echo "  数据库管理: http://localhost:8080 (Adminer)"
    echo
    log_info "管理命令:"
    echo "  查看日志: docker-compose --env-file .env.production logs -f"
    echo "  查看状态: docker-compose --env-file .env.production ps"
    echo "  停止服务: docker-compose --env-file .env.production down"
    echo "  重启服务: docker-compose --env-file .env.production restart"
    echo
    log_info "数据库连接信息:"
    echo "  主机: localhost"
    echo "  端口: 5432"
    echo "  数据库: octi"
    echo "  用户: postgres"
    echo "  密码: octi123456"
    echo
    log_info "Redis连接信息:"
    echo "  主机: localhost"
    echo "  端口: 6379"
    echo
}

# 错误处理
cleanup_on_error() {
    if [ $? -ne 0 ]; then
        log_error "部署过程中发生错误"
        log_info "查看错误日志:"
        docker-compose --env-file .env.production logs --tail 30
        echo
        log_info "常见问题排查:"
        echo "1. 检查端口是否被占用: netstat -tlnp | grep -E '(3000|5432|6379)'"
        echo "2. 检查Docker资源: docker system df"
        echo "3. 查看容器状态: docker-compose --env-file .env.production ps"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "  OCTI智能评估系统 - 腾讯云部署脚本"
    echo "  版本: v4.0.0"
    echo "========================================"
    echo

    # 解析参数
    parse_arguments "$@"

    # 设置错误处理
    trap cleanup_on_error EXIT

    # 显示部署配置
    log_info "部署配置:"
    echo "  部署模式: $DEPLOYMENT_MODE"
    echo "  强制重建: $FORCE_REBUILD"
    if [[ -n "$DOMAIN" ]]; then
        echo "  域名: $DOMAIN"
    fi
    echo

    # 确认部署
    if [[ "$FORCE_REBUILD" == "true" ]]; then
        log_warning "⚠️  完全清理模式将删除现有容器和镜像"
    fi
    
    read -p "确认要部署到腾讯云生产环境吗？[y/N]: " CONFIRM
    if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi

    # 执行部署步骤
    check_dependencies
    check_environment
    cleanup_existing
    build_images
    deploy_services
    init_database
    health_check
    show_deployment_info

    log_success "🎉 腾讯云生产环境部署成功完成！"
}

# 运行主函数
main "$@"
