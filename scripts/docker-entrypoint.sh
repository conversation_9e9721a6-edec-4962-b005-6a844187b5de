#!/bin/bash

# OCTI智能评估系统 - Docker容器启动脚本
# 在应用启动前自动处理数据库迁移和环境准备

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[STARTUP]${NC} $1"; }
log_success() { echo -e "${GREEN}[STARTUP]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[STARTUP]${NC} $1"; }
log_error() { echo -e "${RED}[STARTUP]${NC} $1"; }

echo "🚀 OCTI智能评估系统启动中..."
echo "================================="

# 1. 等待数据库就绪
wait_for_database() {
    log_info "等待数据库连接..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if npx prisma db push --accept-data-loss --skip-generate >/dev/null 2>&1; then
            log_success "数据库连接成功"
            return 0
        fi
        
        log_info "等待数据库启动... ($attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "数据库连接超时"
    return 1
}

# 2. 执行数据库迁移
run_database_migration() {
    log_info "执行数据库迁移..."
    
    # 首先尝试标准迁移
    if npx prisma migrate deploy 2>/dev/null; then
        log_success "数据库迁移成功"
        return 0
    fi
    
    log_warning "标准迁移失败，尝试智能修复..."
    
    # 检查是否是P3005错误（数据库不为空）
    local migration_output=$(npx prisma migrate deploy 2>&1 || true)
    
    if echo "$migration_output" | grep -q "P3005\|database is not empty"; then
        log_info "检测到P3005错误，标记现有数据库为基线..."
        
        # 获取最新的迁移文件并标记为已应用
        local latest_migration=$(find prisma/migrations -name "*.sql" 2>/dev/null | tail -1 | xargs basename -s .sql 2>/dev/null || echo "")
        
        if [[ -n "$latest_migration" ]]; then
            log_info "标记迁移为已应用: $latest_migration"
            if npx prisma migrate resolve --applied "$latest_migration" 2>/dev/null; then
                log_success "基线标记成功"
                
                # 再次尝试迁移
                if npx prisma migrate deploy 2>/dev/null; then
                    log_success "基线标记后迁移成功"
                    return 0
                fi
            fi
        else
            log_info "未找到迁移文件，可能是首次部署"
        fi
    fi
    
    # 如果迁移仍然失败，尝试强制同步
    log_warning "尝试强制数据库同步..."
    if npx prisma db push --accept-data-loss 2>/dev/null; then
        log_success "数据库强制同步成功"
        return 0
    fi
    
    log_warning "数据库迁移失败，但继续启动应用（可能是权限或网络问题）"
    return 0  # 不阻止应用启动
}

# 3. 生成Prisma客户端
generate_prisma_client() {
    log_info "生成Prisma客户端..."
    
    if npx prisma generate 2>/dev/null; then
        log_success "Prisma客户端生成成功"
    else
        log_warning "Prisma客户端生成失败，使用现有客户端"
    fi
}

# 4. 修复权限问题
fix_permissions() {
    log_info "检查和修复文件权限..."
    
    # 修复Prisma客户端权限
    if [[ ! -r /app/node_modules/.prisma/client/index.js ]]; then
        log_info "修复Prisma客户端权限..."
        chmod -R 755 /app/node_modules/.prisma 2>/dev/null || true
        chmod -R 755 /app/node_modules/@prisma 2>/dev/null || true
    fi
    
    log_success "权限检查完成"
}

# 5. 健康检查准备
prepare_health_check() {
    log_info "准备健康检查端点..."
    
    # 确保健康检查端点可用
    # 这里可以添加额外的健康检查准备逻辑
    
    log_success "健康检查准备完成"
}

# 主启动流程
main() {
    log_info "开始应用启动流程..."
    
    # 只在生产环境执行数据库相关操作
    if [[ "$NODE_ENV" == "production" ]]; then
        # 等待数据库
        if ! wait_for_database; then
            log_error "数据库连接失败，但继续启动应用"
        fi
        
        # 执行迁移
        run_database_migration
        
        # 生成客户端
        generate_prisma_client
    else
        log_info "开发环境，跳过数据库迁移"
    fi
    
    # 修复权限
    fix_permissions
    
    # 准备健康检查
    prepare_health_check
    
    log_success "应用启动准备完成"
    echo "================================="
    log_info "启动应用: $*"
    echo ""
    
    # 启动应用
    exec "$@"
}

# 错误处理
trap 'log_error "启动脚本异常退出"; exit 1' ERR

# 执行主流程
main "$@"
