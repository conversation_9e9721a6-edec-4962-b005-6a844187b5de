#!/bin/bash

# OCTI部署问题诊断脚本
# 用于收集详细的系统和Docker信息，帮助诊断部署问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔍 OCTI部署问题诊断脚本"
echo "========================="
echo "收集系统信息以帮助诊断部署问题"
echo ""

# 1. 系统基本信息
log_info "1. 收集系统基本信息..."
echo "=== 系统信息 ==="
uname -a
cat /etc/os-release
echo ""
echo "=== 内存使用情况 ==="
free -h
echo ""
echo "=== 磁盘使用情况 ==="
df -h
echo ""
echo "=== CPU信息 ==="
lscpu | head -10
echo ""

# 2. Docker信息
log_info "2. 收集Docker信息..."
echo "=== Docker版本 ==="
docker --version
docker-compose --version
echo ""
echo "=== Docker系统信息 ==="
docker system df
echo ""
echo "=== Docker镜像列表 ==="
docker images | head -20
echo ""
echo "=== Docker容器状态 ==="
docker ps -a
echo ""
echo "=== Docker网络 ==="
docker network ls
echo ""
echo "=== Docker数据卷 ==="
docker volume ls
echo ""

# 3. OCTI项目信息
log_info "3. 收集OCTI项目信息..."
if [[ -f "package.json" ]]; then
    echo "=== package.json中的Prisma版本 ==="
    grep -E "(prisma|@prisma/client)" package.json || echo "未找到Prisma依赖"
    echo ""
fi

if [[ -f "docker-compose.yml" ]]; then
    echo "=== docker-compose.yml配置检查 ==="
    docker-compose config --quiet && echo "✅ docker-compose.yml语法正确" || echo "❌ docker-compose.yml语法错误"
    echo ""
fi

# 4. 容器日志（如果存在）
log_info "4. 收集容器日志..."
for container in octi-app octi_app_full octi-postgres octi_postgres_full octi-redis octi_redis_full; do
    if docker ps -a --format "{{.Names}}" | grep -q "^${container}$"; then
        echo "=== $container 容器日志（最后50行） ==="
        docker logs "$container" --tail 50 2>&1 || echo "无法获取日志"
        echo ""
    fi
done

# 5. 网络连接检查
log_info "5. 检查网络连接..."
echo "=== 端口占用情况 ==="
netstat -tlnp | grep -E "(3000|5432|6379|8080)" || echo "未发现相关端口占用"
echo ""

# 6. Prisma相关检查
log_info "6. Prisma相关检查..."
if docker ps --format "{{.Names}}" | grep -q "octi-app\|octi_app_full"; then
    APP_CONTAINER=$(docker ps --format "{{.Names}}" | grep -E "octi-app|octi_app_full" | head -1)
    echo "=== $APP_CONTAINER 中的Prisma状态 ==="
    docker exec "$APP_CONTAINER" sh -c "
        echo '检查Prisma客户端文件:'
        ls -la /app/node_modules/.prisma/client/ 2>/dev/null | head -5 || echo '❌ Prisma客户端目录不存在'
        echo ''
        echo '检查已安装的Prisma版本:'
        npm list prisma @prisma/client --depth=0 2>/dev/null || echo '❌ 无法获取Prisma版本信息'
        echo ''
        echo '检查Prisma CLI:'
        npx prisma --version 2>/dev/null || echo '❌ Prisma CLI不可用'
    " 2>/dev/null || echo "❌ 无法连接到应用容器"
    echo ""
fi

# 7. 数据库连接检查
log_info "7. 数据库连接检查..."
if docker ps --format "{{.Names}}" | grep -q "octi-postgres\|octi_postgres_full"; then
    DB_CONTAINER=$(docker ps --format "{{.Names}}" | grep -E "octi-postgres|octi_postgres_full" | head -1)
    echo "=== $DB_CONTAINER 数据库状态 ==="
    docker exec "$DB_CONTAINER" sh -c "
        echo '数据库连接测试:'
        pg_isready -U postgres -d octi && echo '✅ 数据库连接正常' || echo '❌ 数据库连接失败'
        echo ''
        echo '数据库表信息:'
        psql -U postgres -d octi -c '\dt' 2>/dev/null || echo '❌ 无法查询数据库表'
    " 2>/dev/null || echo "❌ 无法连接到数据库容器"
    echo ""
fi

# 8. 健康检查
log_info "8. 应用健康检查..."
echo "=== 应用健康检查 ==="
for port in 3000 8080; do
    if curl -f -s "http://localhost:$port/api/health" >/dev/null 2>&1; then
        echo "✅ 端口 $port 健康检查通过"
        curl -s "http://localhost:$port/api/health" | head -3
    else
        echo "❌ 端口 $port 健康检查失败"
    fi
done
echo ""

# 9. 构建缓存信息
log_info "9. 构建缓存信息..."
echo "=== 构建缓存 ==="
if [[ -d "$HOME/.octi-build-cache" ]]; then
    echo "缓存目录大小: $(du -sh $HOME/.octi-build-cache 2>/dev/null | cut -f1)"
    echo "缓存文件数量: $(find $HOME/.octi-build-cache -type f 2>/dev/null | wc -l)"
else
    echo "未找到构建缓存目录"
fi

if [[ -f ".build-hash" ]]; then
    echo "构建哈希文件存在，最后修改: $(stat -c %y .build-hash 2>/dev/null)"
else
    echo "未找到构建哈希文件"
fi
echo ""

# 10. 环境变量检查
log_info "10. 环境变量检查..."
echo "=== 环境变量文件 ==="
for env_file in .env .env.production .env.example; do
    if [[ -f "$env_file" ]]; then
        echo "✅ $env_file 存在"
        echo "文件大小: $(wc -l < "$env_file") 行"
    else
        echo "❌ $env_file 不存在"
    fi
done
echo ""

log_success "诊断信息收集完成！"
echo ""
echo "📋 问题排查建议："
echo "1. 检查Docker容器状态和日志"
echo "2. 验证Prisma版本一致性"
echo "3. 确认数据库连接正常"
echo "4. 检查端口占用情况"
echo "5. 验证环境变量配置"
echo ""
echo "🔧 常见解决方案："
echo "1. 如果Prisma版本不一致，运行: ./scripts/deploy-tencent-cloud-octi.sh clean"
echo "2. 如果容器启动失败，检查日志并重新构建"
echo "3. 如果数据库连接失败，检查网络和权限配置"
echo "4. 如果端口被占用，停止冲突的服务"
echo ""
