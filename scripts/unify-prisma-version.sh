#!/bin/bash

# OCTI项目Prisma版本统一脚本
# 确保整个项目中所有涉及Prisma版本的地方都统一使用5.22.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 目标版本
TARGET_VERSION="5.22.0"

echo "🔧 OCTI项目Prisma版本统一脚本"
echo "================================="
echo "目标版本: $TARGET_VERSION"
echo ""

# 1. 检查当前版本状态
check_current_versions() {
    log_info "检查当前Prisma版本状态..."
    
    echo "=== package.json中的版本 ==="
    if [[ -f "package.json" ]]; then
        local prisma_version=$(grep -o '"prisma": "[^"]*"' package.json | cut -d'"' -f4)
        local client_version=$(grep -o '"@prisma/client": "[^"]*"' package.json | cut -d'"' -f4)
        
        echo "Prisma CLI: $prisma_version"
        echo "@prisma/client: $client_version"
        
        if [[ "$prisma_version" != "$TARGET_VERSION" ]] || [[ "$client_version" != "$TARGET_VERSION" ]]; then
            log_warning "版本不一致，需要修复"
            return 1
        else
            log_success "package.json版本正确"
        fi
    else
        log_error "未找到package.json文件"
        return 1
    fi
    
    echo ""
    echo "=== package-lock.json中的版本 ==="
    if [[ -f "package-lock.json" ]]; then
        local lock_prisma_count=$(grep -c "\"version\": \"$TARGET_VERSION\"" package-lock.json | head -1)
        echo "找到 $lock_prisma_count 个匹配的版本条目"
    fi
    
    echo ""
}

# 2. 修复package.json
fix_package_json() {
    log_info "修复package.json中的Prisma版本..."
    
    if [[ ! -f "package.json" ]]; then
        log_error "未找到package.json文件"
        return 1
    fi
    
    # 备份原文件
    cp package.json package.json.backup.$(date +%Y%m%d_%H%M%S)
    
    # 修复prisma版本
    if grep -q '"prisma":' package.json; then
        sed -i.tmp 's/"prisma": "[^"]*"/"prisma": "'$TARGET_VERSION'"/g' package.json
        log_success "已修复prisma版本为 $TARGET_VERSION"
    fi
    
    # 修复@prisma/client版本
    if grep -q '"@prisma/client":' package.json; then
        sed -i.tmp 's/"@prisma\/client": "[^"]*"/"@prisma\/client": "'$TARGET_VERSION'"/g' package.json
        log_success "已修复@prisma/client版本为 $TARGET_VERSION"
    fi
    
    # 清理临时文件
    rm -f package.json.tmp
    
    log_success "package.json修复完成"
}

# 3. 重新生成package-lock.json
regenerate_lock_file() {
    log_info "重新生成package-lock.json..."

    # 备份并删除旧的lock文件和node_modules
    if [[ -f "package-lock.json" ]]; then
        cp package-lock.json package-lock.json.backup.$(date +%Y%m%d_%H%M%S)
        log_info "已备份package-lock.json"
        rm package-lock.json
        log_info "已删除旧的package-lock.json"
    fi
    
    if [[ -d "node_modules" ]]; then
        log_warning "删除node_modules目录（这可能需要一些时间）..."
        rm -rf node_modules
    fi
    
    # 重新安装依赖
    log_info "重新安装依赖..."
    npm install
    
    log_success "package-lock.json重新生成完成"
}

# 4. 检查和修复所有文件中的硬编码版本
fix_all_files() {
    log_info "检查和修复所有文件中的硬编码版本..."

    # 检查脚本文件
    local script_files=(
        "scripts/deploy-tencent-cloud-octi.sh"
        "scripts/diagnose-deployment-issues.sh"
        "Dockerfile"
    )

    for file in "${script_files[@]}"; do
        if [[ -f "$file" ]]; then
            log_info "检查脚本文件: $file"

            # 检查是否有硬编码的版本号（非5.22.0）
            if grep -q "prisma@[0-9]" "$file" && ! grep -q "prisma@$TARGET_VERSION" "$file"; then
                log_warning "发现硬编码版本，需要手动检查: $file"
                grep -n "prisma@[0-9]" "$file" || true
            fi

            if grep -q "@prisma/client@[0-9]" "$file" && ! grep -q "@prisma/client@$TARGET_VERSION" "$file"; then
                log_warning "发现硬编码版本，需要手动检查: $file"
                grep -n "@prisma/client@[0-9]" "$file" || true
            fi
        fi
    done

    # 检查和修复文档文件
    log_info "检查和修复文档文件中的版本引用..."

    # 查找所有包含Prisma版本的文档文件
    local doc_files=$(find . -name "*.md" -not -path "./node_modules/*" -not -path "./.next/*" -not -path "./dist/*" | xargs grep -l "@prisma/client.*5\.[0-9]" 2>/dev/null || true)

    if [[ -n "$doc_files" ]]; then
        echo "$doc_files" | while read -r file; do
            if [[ -f "$file" ]]; then
                log_info "检查文档文件: $file"

                # 检查是否有非目标版本
                if grep -q "@prisma/client.*5\.[0-9]" "$file" && ! grep -q "@prisma/client.*$TARGET_VERSION" "$file"; then
                    log_warning "发现旧版本引用: $file"

                    # 备份文件
                    cp "$file" "$file.backup.$(date +%Y%m%d_%H%M%S)"

                    # 修复版本引用（保守处理，只替换明确的版本模式）
                    if grep -q '"@prisma/client": "\^5\.[0-9][0-9]*\.[0-9]"' "$file"; then
                        sed -i.tmp 's/"@prisma\/client": "\^5\.[0-9][0-9]*\.[0-9]"/"@prisma\/client": "'$TARGET_VERSION'"/g' "$file"
                        log_success "已修复 $file 中的版本引用"
                    elif grep -q '"@prisma/client": "5\.[0-9][0-9]*\.[0-9]"' "$file"; then
                        sed -i.tmp 's/"@prisma\/client": "5\.[0-9][0-9]*\.[0-9]"/"@prisma\/client": "'$TARGET_VERSION'"/g' "$file"
                        log_success "已修复 $file 中的版本引用"
                    fi

                    # 清理临时文件
                    rm -f "$file.tmp"
                fi
            fi
        done
    else
        log_success "未发现需要修复的文档文件"
    fi
}

# 5. 验证修复结果
verify_fix() {
    log_info "验证修复结果..."
    
    echo "=== 验证package.json ==="
    local prisma_version=$(grep -o '"prisma": "[^"]*"' package.json | cut -d'"' -f4)
    local client_version=$(grep -o '"@prisma/client": "[^"]*"' package.json | cut -d'"' -f4)
    
    echo "Prisma CLI: $prisma_version"
    echo "@prisma/client: $client_version"
    
    if [[ "$prisma_version" == "$TARGET_VERSION" ]] && [[ "$client_version" == "$TARGET_VERSION" ]]; then
        log_success "✅ package.json版本验证通过"
    else
        log_error "❌ package.json版本验证失败"
        return 1
    fi
    
    echo ""
    echo "=== 验证安装的版本 ==="
    if [[ -d "node_modules" ]]; then
        if npm list prisma @prisma/client --depth=0 2>/dev/null; then
            log_success "✅ 安装版本验证通过"
        else
            log_warning "⚠️ 无法验证安装版本，可能需要重新安装"
        fi
    else
        log_info "node_modules不存在，跳过安装版本验证"
    fi
}

# 6. 生成版本统一报告
generate_report() {
    local report_file="prisma-version-unify-report.md"
    
    log_info "生成版本统一报告..."
    
    cat > "$report_file" << EOF
# Prisma版本统一报告

**统一时间:** $(date)
**目标版本:** $TARGET_VERSION

## 修复内容

### 1. package.json修复
- 统一prisma版本为 $TARGET_VERSION
- 统一@prisma/client版本为 $TARGET_VERSION

### 2. package-lock.json重新生成
- 删除旧的package-lock.json
- 重新安装依赖以确保版本一致性

### 3. 脚本文件检查
- 检查部署脚本中的硬编码版本
- 检查Dockerfile中的版本引用

## 验证结果

### package.json版本
EOF

    # 添加当前版本信息
    if [[ -f "package.json" ]]; then
        echo "- Prisma CLI: $(grep -o '"prisma": "[^"]*"' package.json | cut -d'"' -f4)" >> "$report_file"
        echo "- @prisma/client: $(grep -o '"@prisma/client": "[^"]*"' package.json | cut -d'"' -f4)" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

### 安装版本验证
EOF

    if [[ -d "node_modules" ]]; then
        echo "\`\`\`" >> "$report_file"
        npm list prisma @prisma/client --depth=0 2>/dev/null >> "$report_file" || echo "无法获取版本信息" >> "$report_file"
        echo "\`\`\`" >> "$report_file"
    else
        echo "node_modules不存在，需要运行 npm install" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

## 后续步骤

1. 运行 \`npm install\` 确保依赖正确安装
2. 运行 \`npx prisma generate\` 重新生成客户端
3. 测试应用启动是否正常
4. 在Docker环境中测试构建

## 备份文件

以下文件已备份：
- package.json.backup.*

如需回滚，可以使用备份文件恢复。
EOF

    log_success "版本统一报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始Prisma版本统一处理..."
    
    # 检查当前状态
    if check_current_versions; then
        log_success "当前版本已经统一，无需修复"
        return 0
    fi
    
    # 确认执行
    echo ""
    log_warning "⚠️  即将执行以下操作："
    echo "  1. 修复package.json中的Prisma版本"
    echo "  2. 备份并重新生成package-lock.json"
    echo "  3. 删除node_modules并重新安装依赖"
    echo "  4. 验证修复结果"
    echo ""
    log_warning "⚠️  注意：package-lock.json将被备份后重新生成"
    echo ""
    
    read -p "确认要继续吗？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消操作"
        exit 0
    fi
    
    # 执行修复
    fix_package_json
    regenerate_lock_file
    fix_all_files
    verify_fix
    generate_report
    
    log_success "🎉 Prisma版本统一完成！"
    echo ""
    echo "📋 后续建议："
    echo "1. 运行 npx prisma generate 重新生成客户端"
    echo "2. 测试应用启动: npm run dev"
    echo "3. 测试Docker构建: docker build -t test ."
    echo "4. 查看详细报告: cat prisma-version-unify-report.md"
}

main "$@"
