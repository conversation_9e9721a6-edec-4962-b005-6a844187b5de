# OCTI智能评估系统 - 统一Dockerfile
# 支持开发和生产环境，通过BUILD_MODE参数控制
#
# 🚀 镜像源优化: 统一使用腾讯云镜像源 (适配腾讯云服务器环境)
# - Docker镜像: mirror.ccs.tencentyun.com/library/
# - APT包源: mirrors.tencentyun.com/ubuntu (Ubuntu Jammy专用)
# - NPM包源: registry.npmmirror.com
# 预期构建时间: 30-60秒 (优化前: 350秒)

# 构建参数
ARG BUILD_MODE=production
ARG NODE_VERSION=20

# ============================================================================
# 开发环境构建
# ============================================================================
FROM mirror.ccs.tencentyun.com/library/node:${NODE_VERSION}-alpine AS development

WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    libc6-compat \
    curl \
    git \
    openssl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# 设置开发环境变量
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000

# 复制包管理文件
COPY package.json package-lock.json ./

# 安装所有依赖（包括开发依赖）
RUN npm ci

# 复制源代码
COPY . .

# 生成Prisma客户端
RUN npx prisma generate

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1

# 启动开发服务器
CMD ["npm", "run", "dev"]

# ============================================================================
# 生产环境构建 - 阶段1: 依赖安装
# ============================================================================
FROM mirror.ccs.tencentyun.com/library/node:${NODE_VERSION}-slim AS deps

WORKDIR /app

# 配置腾讯云Ubuntu镜像源并安装系统依赖（一次性完成）
RUN rm -f /etc/apt/sources.list.d/docker.list && \
    # 确保 sources.list 存在（适用于最小化镜像）
    test -f /etc/apt/sources.list || echo "deb http://mirrors.tencentyun.com/ubuntu jammy main restricted universe multiverse" > /etc/apt/sources.list && \
    # 替换现有镜像源
    sed -i 's|archive.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' /etc/apt/sources.list && \
    sed -i 's|security.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' /etc/apt/sources.list && \
    # 处理 sources.list.d 目录下的文件
    find /etc/apt/sources.list.d -name "*.list" -exec sed -i 's|archive.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' {} \; 2>/dev/null || true && \
    find /etc/apt/sources.list.d -name "*.sources" -exec sed -i 's|archive.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' {} \; 2>/dev/null || true && \
    # 更新并安装
    apt-get update && \
    apt-get install -y --no-install-recommends \
        openssl \
        ca-certificates \
        curl \
        libssl3 && \
    # 清理
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean

# 创建SSL 1.1兼容性符号链接
RUN ln -sf /usr/lib/x86_64-linux-gnu/libssl.so.3 /usr/lib/x86_64-linux-gnu/libssl.so.1.1 || \
    ln -sf /usr/lib/aarch64-linux-gnu/libssl.so.3 /usr/lib/aarch64-linux-gnu/libssl.so.1.1 || \
    ln -sf /lib/x86_64-linux-gnu/libssl.so.3 /lib/x86_64-linux-gnu/libssl.so.1.1 || \
    ln -sf /lib/aarch64-linux-gnu/libssl.so.3 /lib/aarch64-linux-gnu/libssl.so.1.1 || \
    true

# 创建SSL 1.1兼容性符号链接
RUN ln -sf /usr/lib/x86_64-linux-gnu/libssl.so.3 /usr/lib/x86_64-linux-gnu/libssl.so.1.1 || \
    ln -sf /usr/lib/aarch64-linux-gnu/libssl.so.3 /usr/lib/aarch64-linux-gnu/libssl.so.1.1 || \
    true

# 配置npm使用国内镜像源加速下载
RUN npm config set registry https://registry.npmmirror.com/ && \
    npm config set fetch-timeout 120000 && \
    npm config set fetch-retry-mintimeout 10000 && \
    npm config set fetch-retry-maxtimeout 60000 && \
    npm config set fetch-retries 3

# 复制包管理文件
COPY package.json package-lock.json ./

# 安装所有依赖（包括开发依赖，构建时需要）
# 添加超时控制避免卡住
RUN timeout 600 npm ci --frozen-lockfile --network-timeout=120000 --prefer-offline --no-audit --no-fund || \
    (echo "npm ci failed, trying with cache clean..." && npm cache clean --force && \
     timeout 600 npm ci --frozen-lockfile --network-timeout=120000 --no-audit --no-fund)

# ============================================================================
# 生产环境构建 - 阶段2: 应用构建
# ============================================================================
FROM mirror.ccs.tencentyun.com/library/node:${NODE_VERSION}-slim AS builder

# 配置腾讯云Ubuntu镜像源并安装系统依赖（一次性完成）
RUN rm -f /etc/apt/sources.list.d/docker.list && \
    # 确保 sources.list 存在（适用于最小化镜像）
    test -f /etc/apt/sources.list || echo "deb http://mirrors.tencentyun.com/ubuntu jammy main restricted universe multiverse" > /etc/apt/sources.list && \
    # 替换现有镜像源
    sed -i 's|archive.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' /etc/apt/sources.list && \
    sed -i 's|security.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' /etc/apt/sources.list && \
    # 处理 sources.list.d 目录下的文件
    find /etc/apt/sources.list.d -name "*.list" -exec sed -i 's|archive.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' {} \; 2>/dev/null || true && \
    find /etc/apt/sources.list.d -name "*.sources" -exec sed -i 's|archive.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' {} \; 2>/dev/null || true && \
    # 更新并安装
    apt-get update && \
    apt-get install -y --no-install-recommends \
        openssl \
        ca-certificates \
        libssl3 && \
    # 清理
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean

# 创建SSL 1.1兼容性符号链接
RUN ln -sf /usr/lib/x86_64-linux-gnu/libssl.so.3 /usr/lib/x86_64-linux-gnu/libssl.so.1.1 || \
    ln -sf /usr/lib/aarch64-linux-gnu/libssl.so.3 /usr/lib/aarch64-linux-gnu/libssl.so.1.1 || \
    ln -sf /lib/x86_64-linux-gnu/libssl.so.3 /lib/x86_64-linux-gnu/libssl.so.1.1 || \
    ln -sf /lib/aarch64-linux-gnu/libssl.so.3 /lib/aarch64-linux-gnu/libssl.so.1.1 || \
    true

# 创建SSL 1.1兼容性符号链接
RUN ln -sf /usr/lib/x86_64-linux-gnu/libssl.so.3 /usr/lib/x86_64-linux-gnu/libssl.so.1.1 || \
    ln -sf /usr/lib/aarch64-linux-gnu/libssl.so.3 /usr/lib/aarch64-linux-gnu/libssl.so.1.1 || \
    true

WORKDIR /app

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules

# 复制源代码
COPY . .

# 设置构建环境变量
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production
ENV PRISMA_CLI_BINARY_TARGETS=debian-openssl-3.0.x

# 严格版本控制的Prisma客户端生成
RUN echo "=== Prisma版本控制 ===" && \
    # 从package.json读取精确版本
    PRISMA_VERSION=$(node -p "require('./package.json').dependencies.prisma || require('./package.json').devDependencies.prisma") && \
    CLIENT_VERSION=$(node -p "require('./package.json').dependencies['@prisma/client'] || require('./package.json').devDependencies['@prisma/client']") && \
    echo "目标Prisma CLI版本: $PRISMA_VERSION" && \
    echo "目标Prisma客户端版本: $CLIENT_VERSION" && \
    # 验证版本一致性
    if [[ "$PRISMA_VERSION" != "$CLIENT_VERSION" ]]; then \
        echo "❌ 版本不一致！CLI: $PRISMA_VERSION, Client: $CLIENT_VERSION" && exit 1; \
    fi && \
    echo "✅ 版本一致性检查通过" && \
    # 清理可能存在的旧版本
    rm -rf node_modules/.prisma/client node_modules/@prisma/client 2>/dev/null || true && \
    # 确保安装的是精确版本
    echo "安装精确版本的Prisma..." && \
    timeout 300 npm install prisma@$PRISMA_VERSION @prisma/client@$CLIENT_VERSION --save-exact --prefer-offline --no-audit --no-fund && \
    # 验证安装的版本
    echo "验证安装版本..." && \
    npm list prisma @prisma/client --depth=0 && \
    # 生成客户端
    echo "生成Prisma客户端..." && \
    timeout 180 npx prisma generate && \
    # 验证生成结果
    ls -la node_modules/.prisma/client/ && \
    echo "✅ Prisma客户端生成完成，版本: $PRISMA_VERSION"

# 构建应用（忽略TypeScript错误）
ENV NEXT_BUILD_IGNORE_TYPESCRIPT_ERRORS=true
ENV NEXT_BUILD_IGNORE_ESLINT_ERRORS=true
RUN npm run build

# ============================================================================
# 生产环境构建 - 阶段3: 运行时
# ============================================================================
FROM mirror.ccs.tencentyun.com/library/node:${NODE_VERSION}-slim AS production

WORKDIR /app

# 创建非root用户
RUN groupadd --system --gid 1001 nodejs
RUN useradd --system --uid 1001 nextjs

# 配置腾讯云Ubuntu镜像源并安装运行时依赖（一次性完成）
RUN rm -f /etc/apt/sources.list.d/docker.list && \
    # 确保 sources.list 存在（适用于最小化镜像）
    test -f /etc/apt/sources.list || echo "deb http://mirrors.tencentyun.com/ubuntu jammy main restricted universe multiverse" > /etc/apt/sources.list && \
    # 替换现有镜像源
    sed -i 's|archive.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' /etc/apt/sources.list && \
    sed -i 's|security.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' /etc/apt/sources.list && \
    # 处理 sources.list.d 目录下的文件
    find /etc/apt/sources.list.d -name "*.list" -exec sed -i 's|archive.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' {} \; 2>/dev/null || true && \
    find /etc/apt/sources.list.d -name "*.sources" -exec sed -i 's|archive.ubuntu.com|mirrors.tencentyun.com/ubuntu|g' {} \; 2>/dev/null || true && \
    # 更新并安装
    apt-get update && \
    apt-get install -y --no-install-recommends \
        dumb-init \
        curl \
        wget \
        openssl \
        ca-certificates \
        libssl3 && \
    # 清理
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean

# 创建SSL 1.1兼容性符号链接
RUN ln -sf /usr/lib/x86_64-linux-gnu/libssl.so.3 /usr/lib/x86_64-linux-gnu/libssl.so.1.1 || \
    ln -sf /usr/lib/aarch64-linux-gnu/libssl.so.3 /usr/lib/aarch64-linux-gnu/libssl.so.1.1 || \
    ln -sf /lib/x86_64-linux-gnu/libssl.so.3 /lib/x86_64-linux-gnu/libssl.so.1.1 || \
    ln -sf /lib/aarch64-linux-gnu/libssl.so.3 /lib/aarch64-linux-gnu/libssl.so.1.1 || \
    true

# 创建SSL 1.1兼容性符号链接
RUN ln -sf /usr/lib/x86_64-linux-gnu/libssl.so.3 /usr/lib/x86_64-linux-gnu/libssl.so.1.1 || \
    ln -sf /usr/lib/aarch64-linux-gnu/libssl.so.3 /usr/lib/aarch64-linux-gnu/libssl.so.1.1 || \
    true

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV PRISMA_CLI_BINARY_TARGETS=debian-openssl-3.0.x

# 安装生产依赖（包含prisma CLI）
COPY package.json package-lock.json ./
RUN timeout 600 npm ci --frozen-lockfile --network-timeout=120000 --prefer-offline --no-audit --no-fund && \
    # 确保prisma CLI可用
    npx prisma --version && \
    # 清理缓存但保留必要的CLI工具
    npm cache clean --force

# 复制构建产物
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# 复制配置文件
COPY --from=builder /app/configs ./configs
COPY --from=builder /app/prisma ./prisma

# 复制Prisma客户端
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma

# 创建启动脚本解决权限问题
RUN echo '#!/bin/bash' > /app/start.sh && \
    echo 'set -e' >> /app/start.sh && \
    echo 'echo "检查Prisma客户端权限..."' >> /app/start.sh && \
    echo 'if [[ ! -r /app/node_modules/.prisma/client/index.js ]]; then' >> /app/start.sh && \
    echo '  echo "修复Prisma客户端权限..."' >> /app/start.sh && \
    echo '  chmod -R 755 /app/node_modules/.prisma 2>/dev/null || true' >> /app/start.sh && \
    echo '  chmod -R 755 /app/node_modules/@prisma 2>/dev/null || true' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo 'echo "启动应用..."' >> /app/start.sh && \
    echo 'exec "$@"' >> /app/start.sh && \
    chmod +x /app/start.sh

# 设置文件权限（确保Prisma客户端目录有正确权限）
RUN chown -R nextjs:nodejs /app && \
    chmod -R 755 /app/node_modules/.prisma && \
    chmod -R 755 /app/node_modules/@prisma && \
    chown nextjs:nodejs /app/start.sh

USER nextjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1

# 启动应用（使用启动脚本）
ENTRYPOINT ["dumb-init", "--", "/app/start.sh"]
CMD ["npm", "start"]

# ============================================================================
# 最终阶段选择器
# ============================================================================
FROM ${BUILD_MODE} AS final