{"name": "octi-intelligent-assessment-system", "version": "4.0.0", "description": "OCTI智能评估系统 - 基于配置驱动和智能体模块化的组织能力评估平台", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose up", "docker:build": "docker build -t octi-production:latest .", "docker:clean": "docker system prune -f", "health": "curl -f http://localhost:3000/api/health || exit 1", "clean": "rm -rf .next node_modules/.cache", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@next/bundle-analyzer": "^14.2.5", "@prisma/client": "5.22.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@types/jspdf": "^2.0.0", "autoprefixer": "^10.4.19", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "express-rate-limit": "^7.4.0", "helmet": "^7.1.0", "html2canvas": "^1.4.1", "ioredis": "^5.4.1", "json5": "^2.2.3", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.408.0", "mermaid": "^11.9.0", "next": "14.2.5", "next-auth": "^4.24.7", "nodemailer": "^6.9.14", "pdfmake": "^0.2.20", "pg": "^8.12.0", "postcss": "^8.4.40", "prisma": "5.22.0", "prom-client": "^15.1.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.1", "react-markdown": "^10.1.0", "recharts": "^2.12.7", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "sharp": "^0.33.4", "tailwind-merge": "^2.4.0", "tailwindcss": "^3.4.7", "tailwindcss-animate": "^1.0.7", "typescript": "^5.5.4", "winston": "^3.13.1", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.14.12", "@types/nodemailer": "^6.4.15", "@types/pg": "^8.11.6", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "playwright": "^1.45.3", "prettier": "^3.3.3", "tsx": "^4.16.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["octi", "assessment", "nonprofit", "ai", "intelligent", "questionnaire", "organization", "capability", "evaluation"], "author": "OCTI Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/octi-team/octi-assessment-system.git"}, "bugs": {"url": "https://github.com/octi-team/octi-assessment-system/issues"}, "homepage": "https://github.com/octi-team/octi-assessment-system#readme"}